fileFormatVersion: 2
guid: 2012b71c22f1262429a544038f981eb2
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: Handle_001
  - first:
      1: 100004
    second: Handle_002
  - first:
      1: 100006
    second: Handle_003
  - first:
      1: 100008
    second: Handle_004
  - first:
      1: 100010
    second: Handles_001
  - first:
      1: 100012
    second: Knob_001
  - first:
      1: 100014
    second: Knob_002
  - first:
      1: 100016
    second: Knob_003
  - first:
      1: 100018
    second: Knob_004
  - first:
      1: 100020
    second: Knobs_001
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: Handle_001
  - first:
      4: 400004
    second: Handle_002
  - first:
      4: 400006
    second: Handle_003
  - first:
      4: 400008
    second: Handle_004
  - first:
      4: 400010
    second: Handles_001
  - first:
      4: 400012
    second: Knob_001
  - first:
      4: 400014
    second: Knob_002
  - first:
      4: 400016
    second: Knob_003
  - first:
      4: 400018
    second: Knob_004
  - first:
      4: 400020
    second: Knobs_001
  - first:
      21: 2100000
    second: No Name
  - first:
      23: 2300000
    second: //RootNode
  - first:
      23: 2300002
    second: Handle_001
  - first:
      23: 2300004
    second: Handle_002
  - first:
      23: 2300006
    second: Handle_003
  - first:
      23: 2300008
    second: Handle_004
  - first:
      23: 2300010
    second: Knob_001
  - first:
      23: 2300012
    second: Knob_002
  - first:
      23: 2300014
    second: Knob_003
  - first:
      23: 2300016
    second: Knob_004
  - first:
      33: 3300000
    second: //RootNode
  - first:
      33: 3300002
    second: Handle_001
  - first:
      33: 3300004
    second: Handle_002
  - first:
      33: 3300006
    second: Handle_003
  - first:
      33: 3300008
    second: Handle_004
  - first:
      33: 3300010
    second: Knob_001
  - first:
      33: 3300012
    second: Knob_002
  - first:
      33: 3300014
    second: Knob_003
  - first:
      33: 3300016
    second: Knob_004
  - first:
      43: 4300000
    second: CabinetLower_Backsplash_30Wx24Dx36H_KN002a
  - first:
      43: 4300002
    second: Knob_004
  - first:
      43: 4300004
    second: Knob_003
  - first:
      43: 4300006
    second: Knob_002
  - first:
      43: 4300008
    second: Knob_001
  - first:
      43: 4300010
    second: Handle_001
  - first:
      43: 4300012
    second: Handle_002
  - first:
      43: 4300014
    second: Handle_003
  - first:
      43: 4300016
    second: Handle_004
  - first:
      95: 9500000
    second: //RootNode
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: No Name
    second: {fileID: 2100000, guid: afb9013263732024fb75caaa0e4a1e38, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 1
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
