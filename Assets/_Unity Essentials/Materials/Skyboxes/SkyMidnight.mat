%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SkyMidnight
  m_Shader: {fileID: 104, guid: 0000000000000000f000000000000000, type: 0}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BackTex:
        m_Texture: {fileID: 2800000, guid: 1a40cbba43c28844d9d5434d798a3784, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DownTex:
        m_Texture: {fileID: 2800000, guid: b37a0b3bf146f5748a2ae0b5856eba8f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FrontTex:
        m_Texture: {fileID: 2800000, guid: f29a90fb9c5a19045b7f1897b30c5777, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LeftTex:
        m_Texture: {fileID: 2800000, guid: 8ccea35bb4bd8a9479b160117e0761f3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RightTex:
        m_Texture: {fileID: 2800000, guid: b712a49fc11d9fd4dbf18d71b3f53e31, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UpTex:
        m_Texture: {fileID: 2800000, guid: e211c2ca9055575428e0614590e7e651, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats: []
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Tint: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
  m_BuildTextureStacks: []
