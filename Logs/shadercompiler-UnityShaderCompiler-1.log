Base path: '/Applications/Unity/Hub/Editor/6000.2.6f2/Unity.app/Contents', plugins path '/Applications/Unity/Hub/Editor/6000.2.6f2/Unity.app/Contents/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=6221 file=Packages/com.unity.render-pipelines.universal/Shaders/SimpleLit.shader name=Universal Render Pipeline/Simple Lit pass=ForwardLit ppOnly=0 stripLineD=0 buildPlatform=2 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_NEEDS_RENDERPASS_FBFETCH_FALLBACK uKW=_ADDITIONAL_LIGHTS dKW=_MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN _ADDITIONAL_LIGHTS_VERTEX EVALUATE_SH_MIXED EVALUATE_SH_VERTEX LIGHTMAP_SHADOW_MIXING SHADOWS_SHADOWMASK _LIGHT_LAYERS _CLUSTER_LIGHT_LOOP PROBE_VOLUMES_L1 PROBE_VOLUMES_L2 DIRLIGHTMAP_COMBINED LIGHTMAP_ON DYNAMICLIGHTMAP_ON USE_LEGACY_LIGHTMAPS LOD_FADE_CROSSFADE DOTS_INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP _RECEIVE_SHADOWS_OFF UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 SHADER_API_GLES31 SHADER_API_GLES32 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=4294967328 lang=3 type=Vertex platform=metal reqs=1 mask=6 start=82 ok=1 outsize=4062

