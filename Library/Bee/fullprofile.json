{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 6484, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 6484, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 6484, "tid": 1064, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 6484, "tid": 1064, "ts": 1759545572244117, "dur": 348, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 6484, "tid": 1064, "ts": 1759545572246611, "dur": 450, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 6484, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 6484, "tid": 1, "ts": 1759545571830197, "dur": 5736, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6484, "tid": 1, "ts": 1759545571835935, "dur": 32019, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6484, "tid": 1, "ts": 1759545571867961, "dur": 29626, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 6484, "tid": 1064, "ts": 1759545572247064, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 6484, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571828922, "dur": 2926, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571831850, "dur": 406554, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571832681, "dur": 2459, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571835144, "dur": 932, "ph": "X", "name": "ProcessMessages 8117", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836078, "dur": 33, "ph": "X", "name": "ReadAsync 8117", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836113, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836118, "dur": 46, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836167, "dur": 23, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836192, "dur": 45, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836242, "dur": 2, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836257, "dur": 40, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836298, "dur": 1, "ph": "X", "name": "ProcessMessages 1788", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836300, "dur": 38, "ph": "X", "name": "ReadAsync 1788", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836340, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836343, "dur": 44, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836388, "dur": 1, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836390, "dur": 30, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836423, "dur": 33, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836459, "dur": 29, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836490, "dur": 1, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836491, "dur": 41, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836533, "dur": 1, "ph": "X", "name": "ProcessMessages 1297", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836535, "dur": 42, "ph": "X", "name": "ReadAsync 1297", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836579, "dur": 36, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836617, "dur": 48, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836668, "dur": 1, "ph": "X", "name": "ProcessMessages 1464", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836669, "dur": 44, "ph": "X", "name": "ReadAsync 1464", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836715, "dur": 1, "ph": "X", "name": "ProcessMessages 1248", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836717, "dur": 48, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836766, "dur": 1, "ph": "X", "name": "ProcessMessages 1678", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836768, "dur": 31, "ph": "X", "name": "ReadAsync 1678", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836800, "dur": 30, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836833, "dur": 33, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836867, "dur": 38, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836908, "dur": 48, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836958, "dur": 33, "ph": "X", "name": "ReadAsync 1487", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571836993, "dur": 39, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571837034, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571837038, "dur": 39, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571837080, "dur": 31, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571837121, "dur": 18, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571837140, "dur": 35, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571837178, "dur": 1018, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838196, "dur": 3, "ph": "X", "name": "ProcessMessages 8136", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838200, "dur": 50, "ph": "X", "name": "ReadAsync 8136", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838252, "dur": 1, "ph": "X", "name": "ProcessMessages 1212", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838254, "dur": 45, "ph": "X", "name": "ReadAsync 1212", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838300, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838302, "dur": 24, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838329, "dur": 42, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838377, "dur": 37, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838415, "dur": 1, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838418, "dur": 37, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838457, "dur": 47, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838506, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838507, "dur": 292, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838802, "dur": 3, "ph": "X", "name": "ProcessMessages 7128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838806, "dur": 39, "ph": "X", "name": "ReadAsync 7128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838846, "dur": 1, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838847, "dur": 71, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838920, "dur": 1, "ph": "X", "name": "ProcessMessages 1844", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838922, "dur": 33, "ph": "X", "name": "ReadAsync 1844", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838957, "dur": 29, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571838993, "dur": 35, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839033, "dur": 40, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839077, "dur": 1, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839099, "dur": 24, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839126, "dur": 1, "ph": "X", "name": "ProcessMessages 1449", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839127, "dur": 44, "ph": "X", "name": "ReadAsync 1449", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839172, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839173, "dur": 39, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839216, "dur": 51, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839268, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839270, "dur": 55, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839328, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839330, "dur": 81, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839417, "dur": 49, "ph": "X", "name": "ReadAsync 1465", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839468, "dur": 1, "ph": "X", "name": "ProcessMessages 2316", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839471, "dur": 38, "ph": "X", "name": "ReadAsync 2316", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839511, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839517, "dur": 46, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839564, "dur": 1, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839567, "dur": 86, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839714, "dur": 1, "ph": "X", "name": "ProcessMessages 1494", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839716, "dur": 38, "ph": "X", "name": "ReadAsync 1494", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839755, "dur": 1, "ph": "X", "name": "ProcessMessages 2688", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839757, "dur": 48, "ph": "X", "name": "ReadAsync 2688", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839818, "dur": 23, "ph": "X", "name": "ReadAsync 1522", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839845, "dur": 2, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839848, "dur": 29, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839880, "dur": 1, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839881, "dur": 33, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839926, "dur": 4, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839930, "dur": 35, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571839976, "dur": 39, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840017, "dur": 1, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840024, "dur": 49, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840080, "dur": 1, "ph": "X", "name": "ProcessMessages 1474", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840082, "dur": 109, "ph": "X", "name": "ReadAsync 1474", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840196, "dur": 54, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840252, "dur": 41, "ph": "X", "name": "ReadAsync 1957", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840294, "dur": 1, "ph": "X", "name": "ProcessMessages 1243", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840298, "dur": 42, "ph": "X", "name": "ReadAsync 1243", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840341, "dur": 3, "ph": "X", "name": "ProcessMessages 1263", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840345, "dur": 27, "ph": "X", "name": "ReadAsync 1263", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840375, "dur": 43, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840440, "dur": 39, "ph": "X", "name": "ReadAsync 1199", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840481, "dur": 32, "ph": "X", "name": "ReadAsync 1672", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840515, "dur": 34, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840551, "dur": 83, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840636, "dur": 1, "ph": "X", "name": "ProcessMessages 1715", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840638, "dur": 36, "ph": "X", "name": "ReadAsync 1715", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840676, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840677, "dur": 38, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840717, "dur": 31, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840782, "dur": 38, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840821, "dur": 1, "ph": "X", "name": "ProcessMessages 1210", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840823, "dur": 35, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840860, "dur": 35, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840898, "dur": 21, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571840967, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841004, "dur": 1, "ph": "X", "name": "ProcessMessages 1445", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841006, "dur": 50, "ph": "X", "name": "ReadAsync 1445", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841058, "dur": 44, "ph": "X", "name": "ReadAsync 1142", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841104, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841105, "dur": 31, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841139, "dur": 22, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841162, "dur": 31, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841196, "dur": 35, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841233, "dur": 24, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841259, "dur": 33, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841301, "dur": 71, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841379, "dur": 31, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841413, "dur": 82, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841495, "dur": 1, "ph": "X", "name": "ProcessMessages 2091", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841497, "dur": 26, "ph": "X", "name": "ReadAsync 2091", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841524, "dur": 42, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841570, "dur": 33, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841604, "dur": 41, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841648, "dur": 37, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841688, "dur": 36, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841773, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841811, "dur": 1, "ph": "X", "name": "ProcessMessages 2638", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841813, "dur": 26, "ph": "X", "name": "ReadAsync 2638", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841850, "dur": 29, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841881, "dur": 36, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841924, "dur": 31, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841957, "dur": 19, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571841978, "dur": 79, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842058, "dur": 1, "ph": "X", "name": "ProcessMessages 1701", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842059, "dur": 28, "ph": "X", "name": "ReadAsync 1701", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842089, "dur": 46, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842137, "dur": 27, "ph": "X", "name": "ReadAsync 1293", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842165, "dur": 22, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842189, "dur": 29, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842219, "dur": 27, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842248, "dur": 34, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842284, "dur": 32, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842318, "dur": 24, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842344, "dur": 32, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842378, "dur": 25, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842405, "dur": 58, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842465, "dur": 39, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842504, "dur": 1, "ph": "X", "name": "ProcessMessages 1806", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842506, "dur": 28, "ph": "X", "name": "ReadAsync 1806", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842536, "dur": 25, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842563, "dur": 33, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842599, "dur": 27, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842627, "dur": 34, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842663, "dur": 29, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842694, "dur": 20, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842715, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842743, "dur": 31, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842775, "dur": 34, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842811, "dur": 36, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842849, "dur": 37, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842888, "dur": 1, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842889, "dur": 43, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842934, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842935, "dur": 35, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571842972, "dur": 38, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843012, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843013, "dur": 39, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843059, "dur": 31, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843093, "dur": 43, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843137, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843139, "dur": 27, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843168, "dur": 32, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843202, "dur": 203, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843406, "dur": 3, "ph": "X", "name": "ProcessMessages 7148", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843409, "dur": 33, "ph": "X", "name": "ReadAsync 7148", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843443, "dur": 9, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843454, "dur": 34, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843491, "dur": 26, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843518, "dur": 40, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843560, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843562, "dur": 32, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843595, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843597, "dur": 29, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843628, "dur": 41, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843672, "dur": 40, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843715, "dur": 26, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843743, "dur": 26, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843771, "dur": 34, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843808, "dur": 37, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843847, "dur": 31, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843881, "dur": 42, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843924, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843926, "dur": 50, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843977, "dur": 1, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571843979, "dur": 43, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844028, "dur": 32, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844067, "dur": 55, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844124, "dur": 32, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844169, "dur": 22, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844194, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844254, "dur": 71, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844328, "dur": 91, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844421, "dur": 78, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844501, "dur": 58, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844562, "dur": 75, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844639, "dur": 54, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844695, "dur": 69, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844765, "dur": 96, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844864, "dur": 68, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571844934, "dur": 67, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845007, "dur": 70, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845079, "dur": 86, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845168, "dur": 61, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845231, "dur": 18, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845251, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845322, "dur": 69, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845396, "dur": 23, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845420, "dur": 5, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845426, "dur": 34, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845469, "dur": 30, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845499, "dur": 4, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845505, "dur": 19, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845544, "dur": 55, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845602, "dur": 74, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845710, "dur": 34, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845746, "dur": 56, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845830, "dur": 27, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845859, "dur": 32, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845899, "dur": 38, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571845947, "dur": 67, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846032, "dur": 7, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846040, "dur": 59, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846116, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846118, "dur": 48, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846171, "dur": 1, "ph": "X", "name": "ProcessMessages 1512", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846173, "dur": 66, "ph": "X", "name": "ReadAsync 1512", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846243, "dur": 28, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846274, "dur": 68, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846349, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846368, "dur": 149, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846566, "dur": 1, "ph": "X", "name": "ProcessMessages 1996", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846570, "dur": 27, "ph": "X", "name": "ReadAsync 1996", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846621, "dur": 2, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846635, "dur": 34, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571846675, "dur": 2301, "ph": "X", "name": "ReadAsync 1337", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571848978, "dur": 3, "ph": "X", "name": "ProcessMessages 8183", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571848983, "dur": 52, "ph": "X", "name": "ReadAsync 8183", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849045, "dur": 11, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849057, "dur": 126, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849185, "dur": 38, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849229, "dur": 49, "ph": "X", "name": "ReadAsync 1469", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849279, "dur": 4, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849590, "dur": 35, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849627, "dur": 2, "ph": "X", "name": "ProcessMessages 4688", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849632, "dur": 43, "ph": "X", "name": "ReadAsync 4688", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849677, "dur": 104, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849784, "dur": 84, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849872, "dur": 40, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849924, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571849926, "dur": 250, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850177, "dur": 1, "ph": "X", "name": "ProcessMessages 3426", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850182, "dur": 31, "ph": "X", "name": "ReadAsync 3426", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850224, "dur": 75, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850300, "dur": 12, "ph": "X", "name": "ProcessMessages 1531", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850313, "dur": 35, "ph": "X", "name": "ReadAsync 1531", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850353, "dur": 35, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850391, "dur": 72, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850464, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850465, "dur": 95, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850562, "dur": 11, "ph": "X", "name": "ProcessMessages 1030", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850576, "dur": 81, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850661, "dur": 76, "ph": "X", "name": "ReadAsync 1631", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850739, "dur": 84, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850825, "dur": 32, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850864, "dur": 84, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571850949, "dur": 52, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851002, "dur": 1, "ph": "X", "name": "ProcessMessages 1508", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851004, "dur": 79, "ph": "X", "name": "ReadAsync 1508", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851086, "dur": 28, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851115, "dur": 10, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851127, "dur": 42, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851172, "dur": 48, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851231, "dur": 31, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851266, "dur": 103, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851370, "dur": 1, "ph": "X", "name": "ProcessMessages 1437", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851682, "dur": 41, "ph": "X", "name": "ReadAsync 1437", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851724, "dur": 5, "ph": "X", "name": "ProcessMessages 6979", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851730, "dur": 101, "ph": "X", "name": "ReadAsync 6979", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851833, "dur": 7, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851861, "dur": 29, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851891, "dur": 1, "ph": "X", "name": "ProcessMessages 1889", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851893, "dur": 83, "ph": "X", "name": "ReadAsync 1889", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571851978, "dur": 35, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852016, "dur": 46, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852064, "dur": 131, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852196, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852363, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852417, "dur": 193, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852611, "dur": 19, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852634, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852676, "dur": 246, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852924, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571852943, "dur": 158, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853102, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853103, "dur": 196, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853301, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853305, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853372, "dur": 192, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853576, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853579, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853631, "dur": 197, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853831, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853862, "dur": 48, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571853915, "dur": 227, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571854179, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571854224, "dur": 171, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571854397, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571854442, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571854488, "dur": 201, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571854691, "dur": 93, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571854785, "dur": 260, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855047, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855051, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855145, "dur": 102, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855249, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855253, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855330, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855395, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855463, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855542, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855544, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855614, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855666, "dur": 45, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855713, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855771, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571855774, "dur": 427, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856302, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856346, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856380, "dur": 93, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856480, "dur": 68, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856640, "dur": 56, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856698, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856701, "dur": 68, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856804, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856867, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856869, "dur": 95, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571856984, "dur": 60, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857063, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857100, "dur": 72, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857189, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857249, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857334, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857427, "dur": 18, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857448, "dur": 109, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857604, "dur": 54, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857661, "dur": 94, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857756, "dur": 7, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857764, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857795, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857801, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571857837, "dur": 177, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858018, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858093, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858146, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858195, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858246, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858295, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858387, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858392, "dur": 51, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858450, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858489, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858510, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858548, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858601, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858654, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858771, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858809, "dur": 34, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858866, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858882, "dur": 84, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571858968, "dur": 48, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859017, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859019, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859117, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859121, "dur": 135, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859258, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859270, "dur": 43, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859315, "dur": 50, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859367, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859458, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859513, "dur": 88, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859604, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859650, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859652, "dur": 45, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859702, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571859808, "dur": 2396, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571862206, "dur": 713, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571862921, "dur": 22277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571885201, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571885410, "dur": 3932, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571889345, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571889347, "dur": 257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571889609, "dur": 1357, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571890968, "dur": 34, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571891003, "dur": 4565, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571895571, "dur": 6532, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571902106, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571902142, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571902218, "dur": 677, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571902898, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571902950, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571903056, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571903198, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571903232, "dur": 241, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571903476, "dur": 329, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571903808, "dur": 246, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904055, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904057, "dur": 327, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904386, "dur": 41, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904429, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904506, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904511, "dur": 268, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904877, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904883, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571904939, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571905162, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571905256, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571905412, "dur": 38, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571905451, "dur": 479, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571905932, "dur": 192, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571906126, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571906386, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571906446, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571906612, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571906809, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571906891, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571906958, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571907128, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571907353, "dur": 233, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571907588, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571907823, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571907863, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571907865, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571908067, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571908276, "dur": 350, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571908627, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571908757, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571909003, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571909147, "dur": 224, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571909373, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571909539, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571909597, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571909654, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571909908, "dur": 286, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571910196, "dur": 215, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571910413, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571910530, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571910648, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571910725, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571910921, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571911195, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571911295, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571911338, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571911433, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571911554, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571911610, "dur": 315, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571911928, "dur": 247, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571912177, "dur": 262, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571912440, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571912540, "dur": 296, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571912849, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571912853, "dur": 389, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571913244, "dur": 383, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571913628, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571913798, "dur": 268, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571914070, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571914072, "dur": 520, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571914594, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571914736, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571914884, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571915127, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571915241, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571915243, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571915434, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571915614, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571915705, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571915866, "dur": 282, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571916149, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571916271, "dur": 92, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571916364, "dur": 269, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571916635, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571916879, "dur": 194, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917075, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917077, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917282, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917423, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917428, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917536, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917563, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917773, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571917955, "dur": 147, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571918104, "dur": 316, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571918424, "dur": 227, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571918652, "dur": 240, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571918894, "dur": 187, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919083, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919192, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919235, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919321, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919360, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919478, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919590, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919695, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919771, "dur": 206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571919978, "dur": 298, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920278, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920447, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920536, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920646, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920650, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920784, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920867, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571920957, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571921082, "dur": 241, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571921323, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571921374, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571921412, "dur": 461, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571921875, "dur": 194, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571922070, "dur": 455, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571922527, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571922653, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571922827, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571922938, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571923074, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571923146, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571923246, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571923493, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571923581, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571923727, "dur": 270, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571923998, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571924062, "dur": 399, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571924464, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571924590, "dur": 430, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571925022, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545571925023, "dur": 87036, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012069, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012071, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012105, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012169, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012199, "dur": 27, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012228, "dur": 19, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012250, "dur": 18, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572012269, "dur": 1176, "ph": "X", "name": "ProcessMessages 2725", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572013448, "dur": 3922, "ph": "X", "name": "ReadAsync 2725", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572017372, "dur": 133, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572017507, "dur": 7248, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572024757, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572024759, "dur": 3181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572027943, "dur": 577, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572028524, "dur": 550, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572029081, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572029085, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572029279, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572029420, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572029707, "dur": 498, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572030215, "dur": 1853, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572032070, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572032160, "dur": 5025, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572037188, "dur": 5266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572042456, "dur": 1130, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572043588, "dur": 705, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572044295, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572044557, "dur": 309, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572044868, "dur": 326, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572045202, "dur": 518, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572045722, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572045854, "dur": 1021, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572046878, "dur": 4134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572051020, "dur": 1439, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572052461, "dur": 437, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572052901, "dur": 474, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572053377, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572053465, "dur": 255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572053722, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572053866, "dur": 2033, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572055901, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572055902, "dur": 2451, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572058356, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572058487, "dur": 412, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572058902, "dur": 576, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572059480, "dur": 279, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572059761, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572059818, "dur": 440, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572060261, "dur": 738, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572061001, "dur": 2683, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572063688, "dur": 460, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572064150, "dur": 327, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572064479, "dur": 1734, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572066215, "dur": 1171, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572067387, "dur": 773, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572068162, "dur": 715, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572068879, "dur": 645, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572069526, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572069682, "dur": 1075, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572070759, "dur": 732, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572071493, "dur": 991, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572072485, "dur": 993, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572073480, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572073610, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572073700, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572073792, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572073890, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074000, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074081, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074137, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074225, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074269, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074395, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074445, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074572, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074621, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074711, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074772, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074898, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572074972, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075022, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075118, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075225, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075384, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075424, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075465, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075586, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075621, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075689, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075853, "dur": 110, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572075965, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076064, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076173, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076231, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076235, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076370, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076512, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076646, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076739, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076847, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572076924, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077023, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077168, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077256, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077346, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077465, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077504, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077540, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077565, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077631, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077665, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077741, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077810, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077846, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077907, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572077973, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078049, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078087, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078179, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078208, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078297, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078346, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078454, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078499, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078575, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078704, "dur": 87, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078793, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078880, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572078959, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079045, "dur": 252, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079299, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079328, "dur": 230, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079560, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079708, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079735, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079818, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572079842, "dur": 54282, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572134131, "dur": 20, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572134152, "dur": 2689, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572136844, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572136847, "dur": 21446, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158297, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158363, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158365, "dur": 37, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158405, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158455, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158459, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158504, "dur": 18, "ph": "X", "name": "ProcessMessages 7225", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572158531, "dur": 3002, "ph": "X", "name": "ReadAsync 7225", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572161534, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572161536, "dur": 337, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572161874, "dur": 18, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572161893, "dur": 70397, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232295, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232297, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232357, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232448, "dur": 41, "ph": "X", "name": "ReadAsync 4612", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232492, "dur": 27, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232522, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232567, "dur": 16, "ph": "X", "name": "ProcessMessages 4886", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572232584, "dur": 3004, "ph": "X", "name": "ReadAsync 4886", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572235590, "dur": 381, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572235973, "dur": 14, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572235988, "dur": 104, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572236094, "dur": 177, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 6484, "tid": 12884901888, "ts": 1759545572236273, "dur": 1816, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 6484, "tid": 1064, "ts": 1759545572247074, "dur": 2392, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 6484, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 6484, "tid": 8589934592, "ts": 1759545571827130, "dur": 70472, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 6484, "tid": 8589934592, "ts": 1759545571897604, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 6484, "tid": 8589934592, "ts": 1759545571897606, "dur": 1630, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 6484, "tid": 1064, "ts": 1759545572249468, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 6484, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 6484, "tid": 4294967296, "ts": 1759545571786947, "dur": 452130, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 6484, "tid": 4294967296, "ts": 1759545571789748, "dur": 32257, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 6484, "tid": 4294967296, "ts": 1759545572239186, "dur": 3421, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 6484, "tid": 4294967296, "ts": 1759545572240617, "dur": 1243, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 6484, "tid": 4294967296, "ts": 1759545572242642, "dur": 5, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 6484, "tid": 1064, "ts": 1759545572249485, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1759545571828572, "dur": 898, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1759545571829476, "dur": 464, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1759545571830047, "dur": 132, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1759545571830422, "dur": 4425, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_FE726D458CD10D04.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1759545571835133, "dur": 1186, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6B2FDB6494F5C18B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1759545571837767, "dur": 640, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1759545571847404, "dur": 1797, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1759545571830186, "dur": 21987, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1759545571852182, "dur": 383977, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1759545572236255, "dur": 486, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1759545571830081, "dur": 22109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571852233, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_3CE642E89F556F9A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571852401, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571852529, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0DCF0FF4901E260E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571852744, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_6507793F6283C281.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571852919, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571853002, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8E09E914FF8F2556.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571853208, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_491D766EB2F76760.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571853371, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571853490, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_924B32A5C81F1D13.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571853649, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571853715, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_036C247230A4A9A5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571853889, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571854036, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_2898C8BD7DB204EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571854227, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571854341, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_C6269DDCB45D40E3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571854499, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571854646, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_6373BDF8A076F05F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571854790, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571854871, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_403154930CF91C99.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571855049, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571855128, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571855289, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571855430, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571855577, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571855704, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571855838, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856004, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856153, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856284, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856413, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856492, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856628, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856752, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571856888, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571857013, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571857179, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571857296, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571857469, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571857629, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571857745, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571857901, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571858036, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571858198, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571858340, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571858498, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571858649, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571858786, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571858936, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571859071, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571859221, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571859364, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571859466, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571859638, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571859785, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571861661, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571863486, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571865040, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571866442, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571867799, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571869346, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571870739, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571872126, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571873574, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571875449, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571877113, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571878812, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571880565, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571882425, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571884204, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571885931, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571887570, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571889305, "dur": 2030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571891336, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571893020, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571894830, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571896465, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571897324, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571899236, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571901053, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571901793, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571901876, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571902295, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571902678, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571904015, "dur": 2885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1759545571906900, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571907100, "dur": 1262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571908421, "dur": 2125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1759545571910546, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571910683, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571910743, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571910810, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571911782, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571911852, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571912690, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1759545571915361, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571915491, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571916472, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1759545571918824, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571918951, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571919005, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571919501, "dur": 1588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1759545571921119, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571922115, "dur": 2040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545571924161, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571924236, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1759545571924644, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1759545571924746, "dur": 90559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545572015306, "dur": 21871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1759545572037178, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545572037320, "dur": 9731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1759545572047052, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545572047130, "dur": 6378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1759545572053508, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545572053623, "dur": 7350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1759545572060974, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545572061069, "dur": 4609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1759545572065690, "dur": 4016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1759545572069752, "dur": 9611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1759545572079426, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1759545572079915, "dur": 156240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571830086, "dur": 22114, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571852261, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_305DF40677C99017.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571852453, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571852599, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_72AE5ADCFC2ADDC3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571852760, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_8B443D88AD263258.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571852928, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571853004, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_C8159F0A8E272DF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571853168, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571853277, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C9273510184DF2BF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571853419, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571853481, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_A91DAC8EC7707957.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571853655, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571853778, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_7BD4FEB767317FD2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571853942, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571854080, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C83500B4868E5201.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571854263, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571854356, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_9CE322C9EB1ABB47.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571854548, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_518B74E2FB7AB39E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571854769, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571854881, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_8D74066172261A51.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571855049, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571855139, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_F6EE2176487706CE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571855292, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571855477, "dur": 6991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571862469, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571862569, "dur": 22511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1759545571885081, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571885328, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571885453, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571885555, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571887231, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571888978, "dur": 1927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571890950, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571891050, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571892859, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571894637, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571896284, "dur": 1719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571898003, "dur": 1944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571899948, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571901721, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571901887, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571902268, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571902702, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571903221, "dur": 1919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1759545571905141, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571905380, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571906382, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571906447, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571906759, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571906843, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1759545571909480, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571909662, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571910428, "dur": 1617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1759545571912045, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571912144, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1759545571914467, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571914686, "dur": 5517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1759545571920203, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571920303, "dur": 744, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545572012129, "dur": 330, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545571921256, "dur": 91218, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1759545572013605, "dur": 14899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1759545572028505, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545572028658, "dur": 15787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1759545572044446, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545572044544, "dur": 8475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1759545572053019, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545572053152, "dur": 6272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1759545572059425, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545572059521, "dur": 7619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1759545572067156, "dur": 4692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1759545572071848, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545572071918, "dur": 7545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1759545572079530, "dur": 82105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1759545572161691, "dur": 356, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1759545572162048, "dur": 74100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571830102, "dur": 22106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571852210, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_449A40027DF30253.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571852365, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571852492, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_C4FD92E2C52775DA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571852690, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571852781, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_D56199BC3A8747B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571852931, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571853031, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_11E33B50EDDD4D77.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571853224, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6DD39E426C8E03AA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571853390, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571853534, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_865B000930F109DF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571853675, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_34CAC2E7B01A0AB3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571853876, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571853979, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_0F93743F3DA89DFB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571854180, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571854299, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_43266BD12BF57FAB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571854485, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571854606, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_A1218431EE46F73E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571854771, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571854924, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571855045, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_9B6B12E2FCF96F59.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571855173, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571855298, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571855474, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571855573, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571855677, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571855806, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571855970, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571856120, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571856262, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571856403, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571856528, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571856668, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571856804, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571856947, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571857097, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571857256, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571857418, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571857574, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571857701, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571857866, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571857989, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571858134, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571858282, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571858424, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571858570, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571858735, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571858877, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571859010, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571859139, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571859299, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571859415, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571859560, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571859799, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571861654, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571863474, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571865032, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571866416, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571867751, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571869314, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571870696, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571872067, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571873520, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571875350, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571877012, "dur": 1716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571878728, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571880475, "dur": 1834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571882309, "dur": 1799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571884113, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571885839, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571887443, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571889165, "dur": 2027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571891192, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571892954, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571894697, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571896384, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571897742, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571900004, "dur": 187, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.6f2/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1759545571900192, "dur": 1141, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.6f2/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 3, "ts": 1759545571901333, "dur": 619, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.6f2/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 3, "ts": 1759545571899738, "dur": 2216, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571901954, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571902274, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571903083, "dur": 1788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571904871, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571904939, "dur": 3294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1759545571908234, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571908513, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1759545571910557, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571910851, "dur": 1174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571912026, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571912092, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571912966, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571913187, "dur": 2009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1759545571915196, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571915337, "dur": 2110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1759545571917448, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571917601, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571917669, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571917773, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571917952, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1759545571918540, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1759545571920862, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571920926, "dur": 1169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571922095, "dur": 2074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545571924169, "dur": 89483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572013653, "dur": 11052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1759545572024706, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572024865, "dur": 18576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1759545572043442, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572043534, "dur": 6207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1759545572049742, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572049831, "dur": 8782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1759545572058613, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572058699, "dur": 5555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1759545572064256, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572064362, "dur": 4693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1759545572069056, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572069152, "dur": 7466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1759545572076619, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572076904, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572076997, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572077072, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572077199, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572077327, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572077528, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572077623, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572077796, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1759545572077886, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078069, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1759545572078119, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078223, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078325, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078470, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1759545572078590, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078719, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078798, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078853, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572078957, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572079070, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572079179, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1759545572079723, "dur": 156415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571830106, "dur": 22110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571852220, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_6EBE0C8E8A1CF9B6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571852407, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571852548, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.LevelPlayModule.dll_E261BC53B0EABC44.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571852727, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_FE726D458CD10D04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571852911, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571852992, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2BF13DCCE9A607BA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571853164, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571853265, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_ADC5D18CB0B46642.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571853459, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_24A2A067F2132054.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571853633, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571853763, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_92DA6E924008A527.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571853938, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571854063, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_4BEF5AC3C463B7A8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571854261, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571854352, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_A2D1B225FFB03EF1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571854504, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571854654, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_6B8ECB8D708B09A4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571854806, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571854911, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_419D8DE6313CFD49.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571855068, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571855171, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571855290, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571855444, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571855630, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571855768, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571855898, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856031, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856197, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856322, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856447, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856559, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856708, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856855, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571856981, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571857130, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571857283, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571857436, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571857590, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571857718, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571857876, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571857998, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571858129, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571858277, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571858418, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571858585, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571858742, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571858900, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571859023, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571859152, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571859311, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571859427, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571859585, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571859738, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571859881, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571861683, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571863500, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571865045, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571866272, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571867620, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571869129, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571870563, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571871900, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571873338, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571875156, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571876808, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571878499, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571880271, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571882074, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571883901, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571885627, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571887282, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571889020, "dur": 1921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571890975, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571891100, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571892863, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571894630, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571896297, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571898109, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571900041, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571901873, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571902303, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571902675, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571903247, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571903305, "dur": 5426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1759545571908732, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571908899, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571908972, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571909172, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571909275, "dur": 4077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1759545571913352, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571913497, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1759545571914703, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571914862, "dur": 2735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1759545571917597, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571917738, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571917793, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571918364, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1759545571920613, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571920696, "dur": 1389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571922086, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571922844, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1759545571922988, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1759545571923667, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545571924171, "dur": 89500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572013671, "dur": 15731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1759545572029402, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572029567, "dur": 15467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1759545572045034, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572045136, "dur": 7502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1759545572052638, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572052730, "dur": 5759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1759545572058534, "dur": 5369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1759545572063903, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572063986, "dur": 4280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1759545572068267, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572068378, "dur": 10478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1759545572078856, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572079034, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572079129, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572079428, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1759545572080029, "dur": 156093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571830119, "dur": 22106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571852228, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C4CB8D8A122179F6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571852474, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_72E258BB18EB20EA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571852684, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571852762, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_9681C97917D7CB22.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571852930, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571853011, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1F30AC1B3E32ADD3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571853239, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_5DC59C432319F425.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571853387, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571853516, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_F8DD117756522C1A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571853627, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571853738, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4DDEE4C0676FABA7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571853899, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571854013, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D097FCF85F9B100C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571854214, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571854314, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FC715F79A53D6B09.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571854493, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571854622, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_66A37991F13CC737.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571854785, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571854895, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_1C4DBA3EEB3A700A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571855064, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571855193, "dur": 7045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571862238, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571862341, "dur": 26818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571889159, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571889440, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571889597, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571889743, "dur": 5912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571895655, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571895716, "dur": 6503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571902294, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571902371, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571902672, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571903680, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571906108, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571906268, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571906384, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571907174, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571910079, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571910273, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571910379, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571911426, "dur": 5108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571916535, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571916717, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571917105, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571919846, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571920059, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571920387, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571920477, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571921452, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571921591, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571922082, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571922236, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571922838, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1759545571922949, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1759545571923734, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545571924165, "dur": 89442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545572013608, "dur": 14318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1759545572027926, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545572028082, "dur": 16048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1759545572044139, "dur": 7058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1759545572051197, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545572051285, "dur": 6680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1759545572057965, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545572058040, "dur": 5080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.Entities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1759545572063162, "dur": 6872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1759545572070035, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1759545572070100, "dur": 9569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1759545572079743, "dur": 156423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571830125, "dur": 22110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571852239, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_C7B54F13DEA2CC26.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571852417, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571852554, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_22D2F92E6B77F466.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571852710, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571852822, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_BC8C09FC68529EF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571852955, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571853052, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C9CDA281B10579AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571853200, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571853301, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6B2FDB6494F5C18B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571853420, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571853486, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_10060989A18D583B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571853744, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_30EFE34738239139.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571853891, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571854006, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_293EBE4C17895349.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571854226, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571854328, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3235BC681CD2D334.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571854486, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571854592, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C0BEF5775ACF93DC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571854778, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571854903, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_FE1DF988AB1AD97A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571855064, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571855189, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571855341, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571855525, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571855644, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571855784, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571855921, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571856058, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_89A9C630F938B4E1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571856252, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571856396, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571856527, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571856654, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571856784, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571856938, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571857071, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571857226, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571857363, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571857520, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571857676, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571857814, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571857958, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571858096, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571858252, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571858386, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571858537, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571858714, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571858853, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571858982, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571859120, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571859265, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571859379, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571859491, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571859669, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571859825, "dur": 1861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571861686, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571863522, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571865050, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571866426, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571867777, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571869322, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571870718, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571872097, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571873535, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571875373, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571877057, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571878789, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571880541, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571882415, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571884181, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571885896, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571887520, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571889247, "dur": 2012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571891259, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571892972, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571894725, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571896404, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571897225, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571898745, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571900610, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571901427, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571901883, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571902317, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571903082, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571903789, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571904152, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571904520, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571904614, "dur": 2606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1759545571907220, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571907369, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571908235, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571908288, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1759545571910626, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571910942, "dur": 1145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571912087, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571912185, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571912693, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571912763, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1759545571915008, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571915183, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571916148, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1759545571918595, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571918768, "dur": 1926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1759545571920694, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571920796, "dur": 1292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571922088, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571923123, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1759545571923323, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1759545571923910, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545571924166, "dur": 89446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545572013615, "dur": 15426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1759545572029042, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545572029171, "dur": 16271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1759545572045443, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545572045571, "dur": 7949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1759545572053521, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545572054009, "dur": 5968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1759545572059977, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545572060060, "dur": 6322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1759545572066384, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545572066479, "dur": 4942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1759545572071422, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1759545572071505, "dur": 8478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.AIIntegration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1759545572080007, "dur": 156111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571830134, "dur": 22103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571852241, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_1774B019F1B36533.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571852396, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571852542, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.InAppPurchasingModule.dll_406B49AB1D7A1F86.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571852735, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_75961BCE6C0A88AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571852910, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571852982, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_E024EBB46A347C7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571853157, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571853257, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_07FA749558856457.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571853386, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571853509, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_537EC7591FCEFB42.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571853655, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571853796, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_E17D4BF58DA20D38.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571853946, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571854104, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_652913876139C508.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571854275, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571854397, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_4C6A0396DDA6B235.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571854527, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571854680, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_D1B3D8CB1E29E148.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571854806, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571854916, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571855049, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_9886A708B72E6D8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571855172, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571855290, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571855462, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571855565, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571855649, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571855791, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571855930, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571856046, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_10511C11633DAFE5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571856244, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571856359, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571856480, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571856602, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571856738, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571856881, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571857001, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571857135, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571857290, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571857460, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571857612, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571857733, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571857883, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571858020, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571858168, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571858314, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571858470, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571858638, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571858777, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571858925, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571859069, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571859215, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571859360, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571859448, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571859616, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571859757, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571859904, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571861707, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571863529, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571865047, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571866428, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571867767, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571869306, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571870693, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571872053, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571873500, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571875331, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571876985, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571878698, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571880450, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571882295, "dur": 1790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571884086, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571885791, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571887448, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571889169, "dur": 1975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571891144, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571892909, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571894669, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571896356, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571898152, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571900087, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571900900, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571901691, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571901895, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571902271, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571902722, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571903021, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571903114, "dur": 1973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571905088, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571905294, "dur": 2538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571907833, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571908077, "dur": 3247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571911325, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571911655, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571911725, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571912468, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571912958, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571913014, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571914854, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571914997, "dur": 2893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571917890, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571918073, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571920481, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571920556, "dur": 1524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571922083, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571922260, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571923101, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1759545571923289, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1759545571923918, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545571924164, "dur": 89442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545572013608, "dur": 15672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1759545572029280, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545572029423, "dur": 15572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1759545572044995, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545572045108, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1759545572045065, "dur": 7957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1759545572053515, "dur": 2478, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545572158366, "dur": 305, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1759545572056335, "dur": 102344, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1759545572161686, "dur": 352, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1759545572162041, "dur": 74106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571830142, "dur": 22105, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571852251, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_49D94D2570102796.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571852419, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571852568, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_A851AF942052CF55.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571852720, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571852853, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8CF4D64BD1E926B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571852973, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_8B43C31E58F93BB5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571853154, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571853253, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_357BEB92C18CEF44.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571853383, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571853500, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_42070571936D1F60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571853662, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571853809, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_4BBBA3F15B83DA76.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571853967, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571854116, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_764489166E4EA1F6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571854280, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571854410, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_09A33E716BF6FA80.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571854574, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1E6BF6774EB4EEFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571854758, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571854858, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_9055C2602955C775.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571855037, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571855200, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5AC12B695C2D0BEE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571855413, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571855552, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571855667, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571855803, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571855949, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571856083, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_59627543C51E81A1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571856264, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571856435, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571856558, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571856684, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571856833, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571856967, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571857144, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571857268, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571857398, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571857558, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571857697, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571857844, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571857986, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571858149, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571858313, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571858453, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571858608, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571858766, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571858915, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571859036, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571859186, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571859323, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571859430, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571859601, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571859737, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571859880, "dur": 1813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571861693, "dur": 1843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571863536, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571865072, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571866472, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571867833, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571869367, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571870758, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571872145, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571873592, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571875468, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571877132, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571878822, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571880562, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571882391, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571884175, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571885898, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571887512, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571889235, "dur": 2001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571891237, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571892969, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571894745, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571896397, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571897236, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571899169, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571900926, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571901693, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571901876, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571902289, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571902669, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571903575, "dur": 1771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1759545571905347, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571905616, "dur": 2359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571908014, "dur": 3499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1759545571911514, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571911695, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_1DF7BBC55D029A75.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571911820, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571912119, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571912655, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1759545571913804, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571913974, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1759545571916266, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571916434, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571916909, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571917239, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1759545571919600, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571919717, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll_B2C8007ACBA256CB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1759545571919801, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571919951, "dur": 2174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571922125, "dur": 2067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545571924192, "dur": 89676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572013870, "dur": 16280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1759545572030150, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572030358, "dur": 15084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1759545572045442, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572045543, "dur": 6883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1759545572052427, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572052524, "dur": 6800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1759545572059378, "dur": 8157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1759545572067535, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572067638, "dur": 5908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1759545572073547, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572073661, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572074060, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572074384, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572074445, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572074709, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572075068, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572075132, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572075189, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572075643, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572076136, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572076682, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572076828, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572077164, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572077257, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Tutorials.Core.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1759545572077410, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572077579, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572077679, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1759545572077730, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572077851, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572077964, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1759545572078051, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1759545572078124, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572078253, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1759545572078389, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572078542, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572078623, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1759545572078678, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572078766, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1759545572078816, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572078881, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1759545572078937, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572079068, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572079139, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572079534, "dur": 156173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1759545572235749, "dur": 341, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1759545572236093, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571830153, "dur": 22098, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571852255, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_47A0488E04E9FB20.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571852484, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_02B01381FE8FF140.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571852689, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571852770, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_81BC176B7F9B820D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571852934, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571853035, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_EC34631C1C319A0D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571853221, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A07316E9EC02DDF3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571853382, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571853496, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_0FE30106E50CC7DE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571853650, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571853771, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_817A34268763458B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571853945, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571854093, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_5E37A07D05D2813A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571854266, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571854382, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7F6F8EF636F80DF6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571854523, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571854686, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_44AE51BDD4E97B92.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571854826, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571854964, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571855096, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_147E4DC932BEDB24.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571855198, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_C22F3212339F1889.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571855405, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571855541, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571855636, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571855774, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571855912, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571856059, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_0CCB9384721BA6A1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571856244, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571856380, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571856484, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571856622, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571856770, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571856918, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571857042, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571857185, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571857323, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571857479, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571857633, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571857766, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571857914, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571858065, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571858212, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571858359, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571858511, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571858667, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571858806, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571858950, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571859075, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571859236, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571859348, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571859444, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571859612, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571859767, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571861644, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571863465, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571864999, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571866370, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571867713, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571869232, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571870659, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571872004, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571873465, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571875289, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571876941, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571878658, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571880401, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571882243, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571884047, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571885776, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571887445, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571889163, "dur": 1990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571891154, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571892906, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571894673, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571896365, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571898058, "dur": 1942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571900000, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571901752, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571901871, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571901956, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571902291, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571902678, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571903451, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571903541, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545571905463, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571905673, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571905726, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571906465, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571906521, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571907440, "dur": 2656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545571910097, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571910352, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571910493, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571911387, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571911497, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545571913932, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571914114, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571914179, "dur": 3936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Tutorials.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545571918116, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571918243, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571918305, "dur": 2025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545571920330, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571920441, "dur": 1657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571922099, "dur": 2055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545571924158, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1759545571924242, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545571924745, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545571925440, "dur": 92024, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545572041651, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1759545572039551, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545572042254, "dur": 275, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545572043661, "dur": 90559, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1759545572134961, "dur": 1338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1759545572136793, "dur": 193, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545572232382, "dur": 363, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1759545572137165, "dur": 95591, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1759545572235754, "dur": 343, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1759545571830165, "dur": 22092, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571852258, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_0A363E957551BA5A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571852448, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571852525, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_E3B0DEB38D118317.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571852711, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571852831, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_ECEBC13D1A308D0C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571852958, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571853064, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_B975A01792F74CAF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571853184, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571853247, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_12AEBD9016974A4D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571853382, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571853450, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_1EBBFFBD35224CC9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571853701, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_490949D7FB633DBA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571853886, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571853996, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_2021C899BB6E595E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571854198, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571854336, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_C251D98B7A8CAF7D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571854497, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571854632, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_23F258A7549724CA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571854784, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571854888, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3BB741C8F9712F8E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571855055, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571855147, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571855236, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571855401, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571855532, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571855746, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571855894, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856029, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856197, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856335, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856468, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856587, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856728, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856869, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571856996, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571857136, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571857259, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571857382, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571857546, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571857689, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571857835, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571857983, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571858115, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571858258, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571858379, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571858556, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571858724, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571858867, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571858996, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571859119, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571859259, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571859385, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571859524, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571859704, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571859893, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571861698, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571863540, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571865063, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571866457, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571867812, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571869352, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571870732, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571872104, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571873546, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571875368, "dur": 1665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571877034, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571878743, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571880479, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571882316, "dur": 1789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571884106, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571885832, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571887467, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571889202, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571891179, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571892920, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571894686, "dur": 1686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571896372, "dur": 1772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571898144, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571900054, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571901650, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571901892, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571902307, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571902682, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571903154, "dur": 3411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1759545571906565, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571906751, "dur": 2567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1759545571909318, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571909518, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571909603, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571909981, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571910654, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571910712, "dur": 6549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1759545571917262, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571917353, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571918271, "dur": 2681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1759545571920953, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571921050, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571922084, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1759545571922219, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1759545571922631, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571922702, "dur": 1460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545571924163, "dur": 89446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545572013610, "dur": 16097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1759545572029708, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545572029850, "dur": 16122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1759545572045972, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545572046067, "dur": 6949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1759545572053063, "dur": 6596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1759545572059659, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1759545572059753, "dur": 6305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1759545572066096, "dur": 4841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1759545572070989, "dur": 8855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1759545572079894, "dur": 156264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571830167, "dur": 22094, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571852262, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_D9F7F83209ACC628.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571852445, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571852503, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_44EB17D09A706242.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571852696, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571852799, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_31874F201A8EB9E9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571852975, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D32BF98FE87CF4D3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571853153, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571853210, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_CE2730F98A0294FC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571853381, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571853454, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_1191B08C64D589A5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571853627, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571853749, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4041D1EA86FB4BB5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571853925, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571854029, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_51594AC5A912A4C5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571854233, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571854320, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_FFE63D8CDE5F7B22.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571854490, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571854669, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_000E419F111F4AF8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571854810, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571854937, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571855065, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_1F223B50B3834349.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571855177, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571855310, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571855448, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_283D5FC3BD6A67A2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571855622, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571855735, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571855878, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856020, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856177, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856285, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856418, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856504, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856641, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856767, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571856905, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571857020, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571857210, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571857339, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571857497, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571857666, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571857792, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571857943, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571858099, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571858264, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571858401, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571858543, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571858708, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571858833, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571858967, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571859106, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571859290, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571859406, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571859527, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571859687, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571859849, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571861677, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571863513, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571865052, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571866450, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571867793, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571869332, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571870727, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571872107, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571873551, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571875412, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571877068, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571878795, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571880555, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571882409, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571884183, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571885900, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571887538, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571889274, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571891319, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571893018, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571894823, "dur": 1648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571896472, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571897286, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571899286, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571901068, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571901892, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571902310, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571902868, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571903403, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571903827, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571904211, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571904283, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571904677, "dur": 2844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571907521, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571907664, "dur": 3355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571911019, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571911238, "dur": 1513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571912802, "dur": 2721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571915524, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571915835, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571915917, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571916035, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571916808, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571916869, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571917331, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571917387, "dur": 1691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571919078, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571919402, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571919463, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571919580, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571920104, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571920604, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571921449, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571921574, "dur": 1574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571923149, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545571923235, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571923400, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571924154, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571924233, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1759545571924641, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1759545571924742, "dur": 89915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572014658, "dur": 17488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1759545572032146, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572032282, "dur": 12420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1759545572044702, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572044829, "dur": 9053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1759545572053883, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572054079, "dur": 5789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Tutorials.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1759545572059869, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572059973, "dur": 4632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1759545572064606, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572064704, "dur": 4695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1759545572069399, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572069455, "dur": 7997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1759545572077453, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572077622, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572077726, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572077938, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572078292, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572078430, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572078552, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572078676, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1759545572078748, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572078868, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572078997, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572079140, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572079217, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1759545572079900, "dur": 156241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571830182, "dur": 22085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571852267, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_02267C0F51A67DCE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571852448, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571852517, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_430C34638A78B4FF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571852693, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571852794, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_735922D8F857CE6F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571852938, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571853021, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_932A8E83893D1D79.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571853215, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_9794E17FFD312E33.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571853380, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571853469, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_A8F0AC30C4EA7773.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571853682, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_8BAC6172A351DB00.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571853874, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571853965, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_3A35C74F5CDC2D64.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571854173, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571854287, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_09E1FFF292596603.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571854484, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571854558, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_6C65DDDBE32CFFA0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571854752, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571854844, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_29965434C6895900.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571855025, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571855122, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2F018D732F244A87.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571855239, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571855420, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571855588, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571855678, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571855820, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571855951, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571856082, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F5791B14F366A51E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571856258, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571856384, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571856510, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571856646, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571856757, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571856913, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571857025, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571857167, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571857317, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571857485, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571857662, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571857778, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571857930, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571858083, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571858232, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571858366, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571858522, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571858686, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571858823, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571858960, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571859087, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571859250, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571859374, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571859482, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571859644, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571859778, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571861671, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571863520, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571865037, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571866419, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571867762, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571869309, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571870710, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571872082, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571873530, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571875345, "dur": 1650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571876995, "dur": 1716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571878711, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571880477, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571882313, "dur": 1785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571884098, "dur": 1737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571885835, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571887457, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571889182, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571891163, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571892897, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571894664, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571896344, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571898098, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571900079, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571900873, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571901642, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571901874, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571902289, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571902680, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571903365, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571903432, "dur": 1351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1759545571904783, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571904941, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571905002, "dur": 2714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1759545571907716, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571907861, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Tutorials.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1759545571911322, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571911572, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Tutorials.Core.ref.dll_9E5F7FA2840E843A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571911664, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571911770, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571912352, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571912409, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571913605, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571913659, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1759545571915789, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571916034, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571916094, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571916973, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571917500, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571917597, "dur": 2088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1759545571919685, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571919847, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571919922, "dur": 1532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571921455, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1759545571921607, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1759545571922104, "dur": 2055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571924159, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545571924646, "dur": 89260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545572013910, "dur": 18152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1759545572032062, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545572032205, "dur": 13691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1759545572045896, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545572045953, "dur": 7554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1759545572053508, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545572053686, "dur": 4847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1759545572058533, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545572058613, "dur": 5188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1759545572063801, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545572063886, "dur": 5923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1759545572069809, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1759545572069882, "dur": 9970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1759545572079909, "dur": 156215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1759545572237560, "dur": 619, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 6484, "tid": 1064, "ts": 1759545572249714, "dur": 2950, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 6484, "tid": 1064, "ts": 1759545572252686, "dur": 1331, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 6484, "tid": 1064, "ts": 1759545572245824, "dur": 8775, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}